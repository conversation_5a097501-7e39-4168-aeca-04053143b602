import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class Encoder(nn.Module):
    """编码器：将节点特征转换为隐藏表示"""
    def __init__(self, input_dim, hidden_dim):
        super(Encoder, self).__init__()
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        
    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        return x

class AttentionDecoder(nn.Module):
    """注意力解码器：使用注意力机制选择下一个节点"""
    def __init__(self, hidden_dim):
        super(AttentionDecoder, self).__init__()
        self.hidden_dim = hidden_dim
        
        # 注意力机制
        self.W1 = nn.Linear(hidden_dim, hidden_dim)
        self.W2 = nn.Linear(hidden_dim, hidden_dim)
        self.v = nn.Linear(hidden_dim, 1)
        
        # 策略和价值网络
        self.policy = nn.Linear(hidden_dim, 1)
        self.value = nn.Linear(hidden_dim, 1)
        
    def forward(self, x, mask=None):
        # 计算注意力分数
        e = torch.tanh(self.W1(x) + self.W2(x))
        attention = self.v(e).squeeze(-1)
        
        # 应用掩码（如果提供）
        if mask is not None:
            attention = attention.masked_fill(mask == 0, -1e9)
        
        # 计算注意力权重
        attention_weights = F.softmax(attention, dim=-1)
        
        # 计算策略和价值
        policy = self.policy(x).squeeze(-1)
        value = self.value(x).squeeze(-1)
        
        return policy, value, attention_weights

class ActorCritic(nn.Module):
    """Actor-Critic网络：结合编码器和解码器"""
    def __init__(self, input_dim, hidden_dim, embedding_dim=None, num_nodes_max=None, capacity=None):
        super(ActorCritic, self).__init__()
        # 如果提供了embedding_dim，使用它；否则使用hidden_dim
        actual_embedding_dim = embedding_dim if embedding_dim is not None else hidden_dim
        self.encoder = Encoder(input_dim, actual_embedding_dim)
        self.decoder = AttentionDecoder(actual_embedding_dim)

        # 存储额外参数（如果需要在其他地方使用）
        self.num_nodes_max = num_nodes_max
        self.capacity = capacity
        
    def forward(self, x, mask=None):
        # 编码输入
        hidden = self.encoder(x)
        
        # 解码并获取策略、价值和注意力权重
        policy, value, attention = self.decoder(hidden, mask)
        
        return policy, value, attention
