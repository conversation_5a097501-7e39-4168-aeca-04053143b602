"""
NPU兼容性工具函数
提供在NPU上可能不被支持的操作的替代实现
"""
import torch
import torch.nn.functional as F
import numpy as np

def npu_safe_multinomial(probs, num_samples=1, replacement=True):
    """
    NPU安全的多项式采样
    如果torch.multinomial在NPU上不被支持，使用CPU实现
    """
    try:
        return torch.multinomial(probs, num_samples, replacement=replacement)
    except RuntimeError as e:
        if "not supported" in str(e).lower():
            # 在CPU上执行，然后移回原设备
            device = probs.device
            probs_cpu = probs.cpu()
            result = torch.multinomial(probs_cpu, num_samples, replacement=replacement)
            return result.to(device)
        else:
            raise e

def npu_safe_where(condition):
    """
    NPU安全的torch.where操作
    """
    try:
        return torch.where(condition)
    except RuntimeError as e:
        if "not supported" in str(e).lower():
            # 使用CPU实现
            device = condition.device
            condition_cpu = condition.cpu()
            result = torch.where(condition_cpu)
            return tuple(r.to(device) for r in result)
        else:
            raise e

def npu_safe_randint(low, high, size, device=None):
    """
    NPU安全的随机整数生成
    """
    try:
        return torch.randint(low, high, size, device=device)
    except RuntimeError as e:
        if "not supported" in str(e).lower():
            # 在CPU上生成，然后移到目标设备
            result = torch.randint(low, high, size, device='cpu')
            if device is not None:
                return result.to(device)
            return result
        else:
            raise e

def npu_safe_masked_fill(tensor, mask, value):
    """
    NPU安全的masked_fill操作
    """
    try:
        return tensor.masked_fill(mask, value)
    except RuntimeError as e:
        if "not supported" in str(e).lower():
            # 使用手动实现
            result = tensor.clone()
            result[mask] = value
            return result
        else:
            raise e

def npu_safe_log_softmax(input, dim=-1):
    """
    NPU安全的log_softmax操作
    """
    try:
        return F.log_softmax(input, dim=dim)
    except RuntimeError as e:
        if "not supported" in str(e).lower():
            # 使用数值稳定的手动实现
            max_vals = torch.max(input, dim=dim, keepdim=True)[0]
            shifted = input - max_vals
            exp_shifted = torch.exp(shifted)
            sum_exp = torch.sum(exp_shifted, dim=dim, keepdim=True)
            return shifted - torch.log(sum_exp)
        else:
            raise e

def npu_safe_softmax(input, dim=-1):
    """
    NPU安全的softmax操作
    """
    try:
        return F.softmax(input, dim=dim)
    except RuntimeError as e:
        if "not supported" in str(e).lower():
            # 使用数值稳定的手动实现
            max_vals = torch.max(input, dim=dim, keepdim=True)[0]
            shifted = input - max_vals
            exp_shifted = torch.exp(shifted)
            sum_exp = torch.sum(exp_shifted, dim=dim, keepdim=True)
            return exp_shifted / sum_exp
        else:
            raise e

def npu_safe_gather(input, dim, index):
    """
    NPU安全的gather操作
    """
    try:
        return input.gather(dim, index)
    except RuntimeError as e:
        if "not supported" in str(e).lower():
            # 使用CPU实现
            device = input.device
            input_cpu = input.cpu()
            index_cpu = index.cpu()
            result = input_cpu.gather(dim, index_cpu)
            return result.to(device)
        else:
            raise e

def npu_safe_argmax(input, dim=-1):
    """
    NPU安全的argmax操作
    """
    try:
        return torch.argmax(input, dim=dim)
    except RuntimeError as e:
        if "not supported" in str(e).lower():
            # 使用CPU实现
            device = input.device
            input_cpu = input.cpu()
            result = torch.argmax(input_cpu, dim=dim)
            return result.to(device)
        else:
            raise e

def check_npu_operation_support():
    """
    检查NPU对各种操作的支持情况
    """
    if not torch.npu.is_available():
        return {"npu_available": False}
    
    device = torch.device("npu:0")
    support_status = {"npu_available": True}
    
    # 测试基本tensor操作
    try:
        x = torch.randn(10, device=device)
        y = torch.randn(10, device=device)
        z = x + y
        support_status["basic_ops"] = True
    except:
        support_status["basic_ops"] = False
    
    # 测试multinomial
    try:
        probs = torch.ones(5, device=device) / 5
        torch.multinomial(probs, 1)
        support_status["multinomial"] = True
    except:
        support_status["multinomial"] = False
    
    # 测试where
    try:
        condition = torch.tensor([True, False, True], device=device)
        torch.where(condition)
        support_status["where"] = True
    except:
        support_status["where"] = False
    
    # 测试randint
    try:
        torch.randint(0, 10, (5,), device=device)
        support_status["randint"] = True
    except:
        support_status["randint"] = False
    
    # 测试softmax
    try:
        x = torch.randn(10, device=device)
        F.softmax(x, dim=-1)
        support_status["softmax"] = True
    except:
        support_status["softmax"] = False
    
    return support_status
