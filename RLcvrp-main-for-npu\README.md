# A3C-CVRP: 基于A3C算法的车辆路径问题求解器

这个项目使用A3C（Asynchronous Advantage Actor-Critic）算法来解决带容量约束的车辆路径问题（CVRP）。

## 环境要求

- Python 3.6+
- PyTorch 1.7+
- NumPy
- tqdm

## 安装依赖

```bash
pip install torch numpy tqdm
```

## 使用方法

### 1. 训练模型

基本训练命令：
```bash
python main.py --save_model --max_episodes 10000
```

这将使用默认参数进行训练，并在训练完成后保存模型。

### 2. 评估模型

使用训练好的模型进行评估：
```bash
python main.py --eval_only --evaluate_episodes 100 --save_eval_raw_data
```

### 3. 参数说明

#### 训练参数
- `--num_workers`: 工作进程数（默认：CPU核心数）
- `--max_episodes`: 总训练回合数（默认：10000）
- `--lr`: 学习率（默认：1e-5）
- `--gamma`: 折扣因子（默认：0.99）
- `--embedding_dim`: 节点嵌入维度（默认：128）
- `--hidden_dim`: 隐藏层维度（默认：128）
- `--value_loss_coeff`: 价值损失系数（默认：0.5）
- `--entropy_coeff`: 熵正则化系数（默认：0.01）

#### 数据生成参数
- `--min_nodes`: 最小客户节点数（默认：10）
- `--max_nodes`: 最大客户节点数（默认：20）
- `--min_capacity`: 最小车辆容量（默认：20.0）
- `--max_capacity`: 最大车辆容量（默认：50.0）
- `--min_demand`: 最小客户需求（默认：1）
- `--max_demand`: 最大客户需求（默认：10）

#### 评估参数
- `--eval_only`: 仅运行评估模式
- `--evaluate_episodes`: 评估实例数量（默认：100）
- `--save_eval_raw_data`: 保存原始评估数据
- `--eval_log_file`: 评估结果文件（默认：evaluation_summary.txt）
- `--eval_raw_data_file`: 原始数据文件（默认：evaluation_raw_data.csv）

#### 模型保存参数
- `--save_model`: 保存训练后的模型
- `--model_save_path`: 模型保存路径（默认：models/cvrp_model.pth）

### 4. 示例命令

#### 使用自定义参数训练
```bash
python main.py --save_model \
    --max_episodes 5000 \
    --lr 1e-4 \
    --embedding_dim 256 \
    --hidden_dim 256 \
    --min_nodes 15 \
    --max_nodes 25
```

#### 使用自定义参数评估
```bash
python main.py --eval_only \
    --evaluate_episodes 200 \
    --save_eval_raw_data \
    --eval_log_file "custom_eval_results.txt"
```

## 输出说明

### 训练输出
- 训练过程中会显示进度条和基本指标
- 训练结束后会显示最后20%回合的平均指标：
  - 平均奖励
  - 平均距离
  - 平均使用车辆数

### 评估输出
- 评估结果会保存在指定的日志文件中
- 包含以下统计信息：
  - 平均总距离
  - 平均使用车辆数
  - 平均计算时间
  - 标准差
  - 最小值
  - 最大值
  - 中位数

## 文件结构
```
.
├── main.py              # 主程序
├── models/             # 模型定义
│   └── actor_critic.py # Actor-Critic网络实现
├── agents/             # 智能体实现
│   └── a3c_agent.py    # A3C算法实现
├── environments/       # 环境定义
│   └── cvrp_env.py    # CVRP环境实现
└── utils/             # 工具函数
    └── data_generator.py # 数据生成器
```

## 注意事项

1. 确保有足够的磁盘空间保存模型和评估结果
2. 训练过程可能需要较长时间，建议使用GPU加速
3. 评估结果会保存在当前目录下
4. 模型文件默认保存在`models`目录下

## 常见问题

1. 如果遇到CUDA相关错误，可以添加`--force_cpu`参数强制使用CPU
2. 如果内存不足，可以减少`--num_workers`的数量
3. 如果训练不稳定，可以尝试调整学习率和其他超参数
